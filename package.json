{"name": "law-compliance-system", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint": "eslint --ext .js,.vue --ignore-path .gitignore ."}, "dependencies": {"@babel/core": "^7.15.5", "@babel/preset-env": "^7.12.17", "@kangc/v-md-editor": "^1.7.12", "@nuxtjs/axios": "^5.13.6", "ant-design-vue": "^1.6.4", "babel-plugin-import": "^1.13.0", "cookie": "^0.4.1", "echarts": "^4.9.0", "highlight.js": "^11.9.0", "less": "^3.12.2", "less-loader": "^7.0.1", "marked": "^2.0.0", "mathjs": "^8.1.0", "moment": "^2.30.1", "nuxt": "^2.15.8", "nuxt-property-decorator": "^2.9.1", "nuxt-socket-io": "^1.1.23", "nuxt-spreadsheet": "0.0.3", "nuxt-vuex-localstorage": "^1.2.7", "reflect-metadata": "^0.2.2", "utf-8-validate": "^5.0.5", "uuid": "^8.3.1", "vue-draggable-resizable": "^2.3.0", "vue-echarts": "^4.1.0", "vue-property-decorator": "^8.0.0"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.27.1", "@nuxt/types": "^2.14.4", "@nuxt/typescript-build": "^2.0.3", "@nuxtjs/eslint-config": "^3.1.0", "@nuxtjs/eslint-config-typescript": "^3.0.0", "@nuxtjs/eslint-module": "^2.0.0", "@nuxtjs/stylelint-module": "^4.0.0", "@types/echarts": "^4.9.22", "@types/lodash": "^4.17.20", "@types/node": "^24.0.4", "@types/vue": "^2.0.0", "@types/vue-router": "^2.0.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "^29.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^6.1.1", "cross-env": "^6.0.3", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-nuxt": "^1.0.0", "eslint-plugin-prettier": "^3.1.4", "prettier": "^2.0.5", "script-loader": "^0.7.2", "stylelint": "^13.7.2", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^20.0.0", "vue-class-component": "^7.2.6"}}